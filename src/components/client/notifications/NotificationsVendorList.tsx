import { scale } from "@/src/_helper/Scaler";
import SkeletonNotificationList from "@/src/components/loader/SkeletonNotificationList";
import { InfiniteScrollList } from "@/src/components/shared/InfiniteScrollList";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { useLanguage } from "@/src/context/LanguageContext";
import useGetNotifications from "@/src/services/querys/vendor/useGetNotifications";
import { INotification } from "@/src/types/notifications";
import React, { useMemo, useState } from "react";
import { StyleSheet, View } from "react-native";
import EmptyNotificationList from "../../ui/EmptyNotificationList";
import { ErrorComponent } from "../../ui/ErrorComponent";
import NotificationListItem from "./NotificationListItem";

const NotificationsList: React.FC = () => {
  const { t } = useLanguage();
  const [refreshing, setRefreshing] = useState(false);

  const {
    data,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    refetch,
  } = useGetNotifications();

  const notifications = useMemo(() => {
    return data?.pages?.flatMap((page) => page.data.items) || [];
  }, [data]);

  const totalItems = data?.pages?.[0]?.data?.totalItems || 0;

  const handleRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const handleRefetch = () => {
    refetch();
  };

  const renderNotificationItem = ({ item }: { item: INotification }) => (
    <NotificationListItem notification={item} />
  );

  if (error) {
    return (
      <ErrorComponent
        error={t("backend.server_error")}
        onRetry={handleRefetch}
      />
    );
  }

  return (
    <View style={styles.section}>
      <ThemedText type="bold" size={18} style={{ marginBottom: scale(16) }}>
        {t("notifications.title")} ({totalItems})
      </ThemedText>
      <InfiniteScrollList
        data={notifications}
        isLoading={isLoading}
        isFetchingNextPage={isFetchingNextPage}
        hasNextPage={hasNextPage}
        fetchNextPage={fetchNextPage}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        estimatedItemSize={80}
        renderItem={renderNotificationItem}
        skeletonComponent={<SkeletonNotificationList />}
        emptyComponent={<EmptyNotificationList />}
        count={8}
        keyExtractor={(item, index) => {
          // Handle skeleton items
          if (item && typeof item === "object" && "__skeleton" in item) {
            return `skeleton-${index}`;
          }
          return item._id;
        }}
      />
    </View>
  );
};

export default NotificationsList;

const styles = StyleSheet.create({
  section: {
    flex: 1,
  },
});
