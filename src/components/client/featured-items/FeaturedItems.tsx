import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import useGetFeatured from "@/src/services/querys/client/useGetFeatured";
import { router } from "expo-router";
import React, { useMemo, useState } from "react";
import {
  Dimensions,
  FlatList,
  Image,
  NativeScrollEvent,
  NativeSyntheticEvent,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import SkeletonFeaturedItem from "../../loader/SkeletonFeaturedItem";
import { ErrorComponentSmall } from "../../ui/ErrorComponentSmall";
import { ThemedText } from "../../ui/ThemedText";

const { width } = Dimensions.get("window");

export interface IFeaturedItem {
  id: string;
  name: string;
  description: string;
  image: string;
  type: "product" | "brand";
}

const FeaturedItems = () => {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const [activeIndex, setActiveIndex] = useState(0);
  const { data, isLoading, isError } = useGetFeatured();

  // Transform API data
  const featuredItems = useMemo(() => {
    return data?.data?.featured || [];
  }, [data]);

  // Handle navigation based on item type
  const handleItemPress = (item: IFeaturedItem) => {
    if (item.type === "product") {
      router.push(`/(client)/(screens)/products/${item.id}/product`);
    } else if (item.type === "brand") {
      router.push(`/(client)/(screens)/brands/${item.id}/brand-details`);
    }
  };

  // Handle scroll to update active dot index
  const onScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const slideSize = event.nativeEvent.layoutMeasurement.width;
    const index = Math.floor(event.nativeEvent.contentOffset.x / slideSize);
    setActiveIndex(index);
  };

  const renderItem = ({ item }: { item: IFeaturedItem }) => (
    <TouchableOpacity
      style={{
        width,
        justifyContent: "flex-start",
      }}
      onPress={() => handleItemPress(item)}
      activeOpacity={0.7}
    >
      <View
        style={{
          flexDirection: "row",
          gap: scale(10),
          paddingHorizontal: scale(15),
          alignItems: "center",
        }}
      >
        <View style={{ flex: 1 }}>
          <ThemedText
            style={{ color: Colors[currentTheme ?? "dark"].secondary }}
          >
            {t("client.featured.label")}
          </ThemedText>
          <ThemedText size={16} type="bold">
            {item.name}
          </ThemedText>
          <ThemedText
            style={{ color: Colors[currentTheme ?? "dark"].secondary }}
            numberOfLines={0} // allow wrapping
          >
            {item.description}
          </ThemedText>
        </View>
        <Image
          source={{ uri: item.image }}
          style={{
            height: scale(93),
            width: scale(138),
            backgroundColor: Colors[currentTheme ?? "dark"].secondary,
            borderRadius: scale(10),
          }}
          resizeMode="cover"
        />
      </View>
    </TouchableOpacity>
  );

  // Loading state
  if (isLoading) {
    return (
      <View>
        <FlatList
          data={[1, 2, 3]} // Show 3 skeleton items
          renderItem={() => <SkeletonFeaturedItem />}
          keyExtractor={(_, index) => `skeleton-${index}`}
          horizontal
          pagingEnabled
          snapToInterval={width}
          snapToAlignment="start"
          decelerationRate="fast"
          showsHorizontalScrollIndicator={false}
          scrollEnabled={false}
        />

        {/* Skeleton dots */}
        <View style={styles.dotsContainer}>
          {[1, 2, 3].map((_, index) => (
            <View
              key={`skeleton-dot-${index}`}
              style={[
                styles.dot,
                { backgroundColor: Colors[currentTheme ?? "dark"].border },
              ]}
            />
          ))}
        </View>
      </View>
    );
  }

  // Error state
  if (isError) {
    return (
      <View style={styles.errorContainer}>
        <ErrorComponentSmall />
      </View>
    );
  }

  // Empty state
  if (!featuredItems || featuredItems.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <ThemedText
          style={{
            color: Colors[currentTheme ?? "dark"].secondary,
            textAlign: "center",
          }}
        >
          {t("client.featured.empty")}
        </ThemedText>
      </View>
    );
  }

  return (
    <View>
      <FlatList
        data={featuredItems}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        horizontal
        pagingEnabled
        snapToInterval={width}
        snapToAlignment="start"
        decelerationRate="fast"
        showsHorizontalScrollIndicator={false}
        onScroll={onScroll}
        scrollEventThrottle={16}
      />

      {/* Dots container */}
      <View style={styles.dotsContainer}>
        {featuredItems.map((_: IFeaturedItem, index: number) => (
          <View
            key={index}
            style={[
              styles.dot,
              {
                backgroundColor:
                  index === activeIndex
                    ? Colors[currentTheme ?? "dark"].primary
                    : Colors[currentTheme ?? "dark"].border,
              },
            ]}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  dotsContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: scale(10),
  },
  dot: {
    width: scale(8),
    height: scale(8),
    borderRadius: scale(20),
    marginHorizontal: scale(4),
  },
  errorContainer: {
    paddingVertical: scale(20),
    paddingHorizontal: scale(15),
  },
  emptyContainer: {
    paddingVertical: scale(40),
    paddingHorizontal: scale(15),
    alignItems: "center",
    justifyContent: "center",
  },
});

export default FeaturedItems;
