import { scale } from "@/src/_helper/Scaler";
import { useCartLayout } from "@/src/components/cart/CartLayout";
import SkeletonProductDetails from "@/src/components/loader/SkeletonProductDetails";
import { ErrorComponent } from "@/src/components/ui/ErrorComponent";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { AppTheme, Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { useCart } from "@/src/hooks/useCart";
import useAddBrandToFavoriteMutation from "@/src/services/querys/client/useAddBrandToFavoriteMutation";
import useGetProductDetails from "@/src/services/querys/client/useGetProductDetails";
import useRemoveFromWishlist from "@/src/services/querys/client/useRemoveFromWishlist";
import { AntDesign } from "@expo/vector-icons";
import { useLocalSearchParams } from "expo-router";
import React, { useRef, useState } from "react";
import {
  Dimensions,
  FlatList,
  Image,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import BottomSheetAddReview from "../../bottom-sheet/BottomSheetAddReview";
import BrandValidationBottomSheet from "../../bottom-sheet/BrandValidationBottomSheet";
import SwipeToRightButton from "../../buttons/SwipeToRightButton";

const { width: screenWidth } = Dimensions.get("window");

const ProductDetails = () => {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [showBrandValidationSheet, setShowBrandValidationSheet] =
    useState(false);
  const [pendingProduct, setPendingProduct] = useState<any>(null);
  const [isReviewSheetVisible, setReviewSheetVisible] = useState(false);
  const flatListRef = useRef<FlatList>(null);
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const theme = currentTheme ?? "dark";
  const styles = getStyles(theme);
  const { id } = useLocalSearchParams() as { id: string };

  // Add favorite mutations
  const mutation = useAddBrandToFavoriteMutation();
  const mutationRm = useRemoveFromWishlist();

  // Cart functionality
  const { addToCart, canAddToCart, clearCart, cartItems } = useCart();
  const { showCartDetails } = useCartLayout();

  const { data, isLoading, error, isError, refetch } = useGetProductDetails(id);
  const handleRefetch = async () => await refetch();

  // Handle add to cart with brand validation
  const handleAddToCart = (product: any) => {
    const productToAdd = {
      _id: product._id,
      name: product.name,
      description: product.description,
      price: product.price,
      images: product.images,
      brandId: product.brand?._id || "",
      brandName: product.brand?.name || "",
    };

    const validation = canAddToCart(productToAdd.brandId);
    if (!validation.canAdd) {
      setPendingProduct(productToAdd);
      setShowBrandValidationSheet(true);
      return;
    }

    const result = addToCart(productToAdd);
    if (result?.success) {
      // Show cart details sheet briefly
      showCartDetails?.();
    }
  };

  // Handle clear cart and add new product
  const handleClearCartAndAdd = () => {
    if (pendingProduct) {
      clearCart();
      addToCart(pendingProduct, true); // Skip validation since we cleared cart
      setPendingProduct(null);
      setShowBrandValidationSheet(false);
      showCartDetails?.();
    }
  };

  if (isLoading) return <SkeletonProductDetails />;
  if (isError)
    return (
      <ErrorComponent
        error={t("backend.server_error")}
        onRetry={handleRefetch}
      />
    );

  const product = data.data.product;

  const handleScroll = (event: any) => {
    const contentOffset = event.nativeEvent.contentOffset;
    const viewSize = event.nativeEvent.layoutMeasurement;
    const pageNum = Math.floor(contentOffset.x / viewSize.width);
    setSelectedImageIndex(pageNum);
  };

  const renderImageItem = ({ item }: { item: string }) => (
    <View style={styles.imageContainer}>
      <Image source={{ uri: item }} style={styles.productImage} />
    </View>
  );

  const renderImageCarousel = () => (
    <View style={styles.imageCarouselContainer}>
      <FlatList
        ref={flatListRef}
        data={product.images}
        renderItem={renderImageItem}
        keyExtractor={(_, index) => index.toString()}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        decelerationRate="fast"
        snapToInterval={screenWidth - scale(30)}
        snapToAlignment="start"
        contentContainerStyle={styles.flatListContent}
      />
    </View>
  );

  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {renderImageCarousel()}

        <View style={styles.productInfo}>
          <View style={styles.productNameContainer}>
            <ThemedText type="bold" size={24} style={styles.productName}>
              {product.name}
            </ThemedText>
            <View style={styles.actionButtons}>
              <TouchableOpacity
                onPress={() => setReviewSheetVisible(true)}
                style={styles.reviewButton}
              >
                <AntDesign
                  name="star"
                  size={24}
                  color={Colors[theme].secondary}
                />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  if (data?.data?.isFavorite) {
                    mutationRm.mutateAsync({ id, type: "product" });
                  } else {
                    mutation.mutateAsync({ id, type: "product" });
                  }
                }}
                disabled={mutation.isPending || mutationRm.isPending}
                style={styles.heartButton}
              >
                <AntDesign
                  name={data?.data?.isFavorite ? "heart" : "hearto"}
                  size={24}
                  color={
                    data?.data?.isFavorite ? "#FF6B6B" : Colors[theme].secondary
                  }
                />
              </TouchableOpacity>
            </View>
          </View>

          <ThemedText size={16} style={styles.productDescription}>
            {product.description}
          </ThemedText>

          <ThemedText type="bold" size={20} style={styles.productPrice}>
            ${product.price}
          </ThemedText>
        </View>
      </ScrollView>

      <View style={styles.bottomActions}>
        <SwipeToRightButton
          title={t("buttons.addToCart")}
          onSwipeComplete={() => handleAddToCart(product)}
          color={Colors[theme].primary}
          style={styles.button}
        />
      </View>

      {/* Brand Validation Bottom Sheet */}
      <BrandValidationBottomSheet
        isVisible={showBrandValidationSheet}
        onClose={() => setShowBrandValidationSheet(false)}
        onClearCartAndAdd={handleClearCartAndAdd}
        currentBrandName={
          cartItems.length > 0 ? cartItems[0].brandName : undefined
        }
        newBrandName={product?.brand?.name}
      />

      {/* Review Bottom Sheet */}
      <BottomSheetAddReview
        isVisible={isReviewSheetVisible}
        onClose={() => setReviewSheetVisible(false)}
        id={product?.brand?._id || ""}
        productId={id}
      />
    </View>
  );
};

const getStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    scrollContent: {
      paddingBottom: scale(100),
    },
    imageCarouselContainer: {
      marginBottom: scale(20),
      height: scale(240),
    },
    flatListContent: {
      alignItems: "center",
    },
    imageContainer: {
      width: scale(240),
      height: scale(240),
      borderRadius: scale(15),
      marginHorizontal: scale(15),
      overflow: "hidden",
      backgroundColor: Colors[theme].product_placeholder,
      justifyContent: "center",
      alignItems: "center",
    },
    productImage: {
      width: "100%",
      height: "100%",
      resizeMode: "cover",
    },
    productInfo: {
      marginBottom: scale(15),
    },
    productNameContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      marginBottom: scale(10),
    },
    productName: {
      flex: 1,
      textAlign: "left",
    },
    actionButtons: {
      flexDirection: "row",
      alignItems: "center",
    },
    reviewButton: {
      padding: scale(5),
      marginRight: scale(10),
    },
    heartButton: {
      padding: scale(5),
    },
    productDescription: {
      lineHeight: scale(22),
      marginBottom: scale(15),
      color: Colors[theme].secondary,
      textAlign: "left",
    },
    productPrice: {
      textAlign: "left",
    },
    bottomActions: {
      backgroundColor: Colors[theme].background,
    },
    button: {
      borderRadius: scale(25),
      marginBottom: scale(10),
    },
  });

export default ProductDetails;
