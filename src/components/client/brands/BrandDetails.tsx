import { scale } from "@/src/_helper/Scaler";
import BottomSheetAddReview from "@/src/components/bottom-sheet/BottomSheetAddReview";
import DefaultButton from "@/src/components/buttons/Default";
import { MainContainerForScreens } from "@/src/components/containers/MainContainerForScreens";
import SkeletonBrandDetails from "@/src/components/loader/BrandDetailsSkelaton";
import EmptyReviewList from "@/src/components/ui/EmptyReviewList";
import { ErrorComponent } from "@/src/components/ui/ErrorComponent";
import ProductReviewCard from "@/src/components/ui/ProductReviewCard";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { AppTheme, Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import useAddBrandToFavoriteMutation from "@/src/services/querys/client/useAddBrandToFavoriteMutation";
import useGetBrandDetails from "@/src/services/querys/client/useGetBrandDetails";
import useRemoveFromWishlist from "@/src/services/querys/client/useRemoveFromWishlist";
import { AntDesign } from "@expo/vector-icons";
import { router, useLocalSearchParams } from "expo-router";
import React, { useState } from "react";
import { Image, StyleSheet, TouchableOpacity, View } from "react-native";

const Brand = () => {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const theme = currentTheme ?? "dark";
  const styles = getStyles(theme);
  const { id } = useLocalSearchParams() as { id: string };
  const mutation = useAddBrandToFavoriteMutation();
  const mutationRm = useRemoveFromWishlist();
  const { data, isError, error, isLoading, refetch } = useGetBrandDetails(id);
  const [isReviewSheetVisible, setReviewSheetVisible] = useState(false);

  const handleRefetch = async () => await refetch();
  const brand = data?.data?.brand;
  const hasReviews = brand?.reviews && brand?.reviews.length > 0;
  const averageRating = brand?.rating || 0;
  const reviewsCount = brand?.reviews.length || 0;

  const renderStars = (rating: number) =>
    Array.from({ length: 5 }, (_, index) => (
      <AntDesign
        key={index}
        name="star"
        size={scale(16)}
        color={index < rating ? Colors[theme].star : Colors[theme].no_star}
        style={styles.starIcon}
      />
    ));

  const renderReview = (review: any) => {
    const userName = review?.user?.name || review?.name || "Anonymous";
    const userInitial = userName.charAt(0).toUpperCase();

    return (
      <View key={review?._id || review?.id} style={styles.reviewContainer}>
        <View style={styles.reviewHeader}>
          <View style={styles.reviewAvatarWrapper}>
            <ThemedText
              type="semi-bold"
              style={{
                color:
                  currentTheme === "dark"
                    ? Colors.dark.black
                    : Colors.light.white,
              }}
            >
              {userInitial}
            </ThemedText>
          </View>
          <View style={styles.reviewUserInfo}>
            <ThemedText type="semi-bold" size={16}>
              {userName}
            </ThemedText>
            <ThemedText style={styles.secondaryText}>
              {review?.timeAgo ||
                new Date(review?.createdAt).toLocaleDateString()}
            </ThemedText>
          </View>
        </View>
        <View style={styles.starContainer}>
          {renderStars(review?.rating || 0)}
        </View>
        <ThemedText size={16} style={styles.reviewText}>
          {review?.comment ||
            review?.review ||
            t("client.brandDetails.noCommentProvided")}
        </ThemedText>
        {/* Show product info if this review is for a specific product */}
        {review?.product && <ProductReviewCard product={review?.product} />}
      </View>
    );
  };

  if (isLoading) return <SkeletonBrandDetails />;
  if (isError || !data?.data?.brand)
    return (
      <ErrorComponent
        onRetry={handleRefetch}
        error={t("backend.server_error")}
      />
    );

  return (
    <View style={{ flex: 1 }}>
      {/* Heart Icon in Top Right */}
      <TouchableOpacity
        onPress={() => {
          if (data?.data?.isFavorite) {
            mutationRm.mutateAsync({ id, type: "brand" });
          } else {
            mutation.mutateAsync({ id, type: "brand" });
          }
        }}
        disabled={mutation.isPending || mutationRm.isPending}
        style={styles.topRightHeartButton}
      >
        <AntDesign
          name={data?.data?.isFavorite ? "heart" : "hearto"}
          size={24}
          color={data?.data?.isFavorite ? "#FF6B6B" : Colors[theme].secondary}
        />
      </TouchableOpacity>

      <MainContainerForScreens>
        <View style={styles.headerSection}>
          <Image
            source={
              brand?.logo
                ? { uri: brand?.logo }
                : require("@/src/assets/images/icon.png")
            }
            style={styles.brandLogo}
          />
          <ThemedText size={22} type="bold" style={styles.brandTitle}>
            {brand?.name}
          </ThemedText>
          <ThemedText size={16} style={styles.brandSubtitle}>
            {brand?.description || t("client.brandDetails.noDescription")}
          </ThemedText>
          <ThemedText size={16} style={styles.brandRating}>
            {averageRating.toFixed(1)} • {reviewsCount} reviews
          </ThemedText>
          <DefaultButton
            title={t("buttons.viewProducts")}
            onPress={() =>
              router.push(`/(client)/(screens)/brands/${id}/products-brand`)
            }
            active
            color={Colors[theme].thirdary}
            style={styles.viewProductsButton}
          />
        </View>

        <View style={styles.section}>
          <ThemedText type="bold" size={22}>
            {t("client.brandDetails.vendor")}
          </ThemedText>
          <View style={styles.vendorInfo}>
            <View style={styles.reviewAvatarWrapper}>
              <ThemedText
                type="semi-bold"
                style={{
                  color:
                    currentTheme === "dark"
                      ? Colors.dark.black
                      : Colors.light.white,
                }}
              >
                {brand?.vendor?.name.charAt(0).toUpperCase()}
              </ThemedText>
            </View>
            <View style={styles.vendorDetails}>
              <ThemedText type="semi-bold" size={16}>
                {brand?.vendor?.businessName ||
                  brand?.vendor?.name ||
                  t("client.brandDetails.unknownVendor")}
              </ThemedText>
              <ThemedText style={styles.secondaryText}>
                {brand?.vendor?.ownerName ||
                  brand?.vendor?.email ||
                  brand?.email}
              </ThemedText>
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <ThemedText type="bold" size={22}>
            {t("client.brandDetails.about")}
          </ThemedText>
          <ThemedText size={16} style={styles.aboutText}>
            {brand?.description ||
              t("client.brandDetails.noDetailedDescription")}
          </ThemedText>

          {brand?.location && (
            <View style={styles.locationContainer}>
              <AntDesign
                name="enviromento"
                size={scale(16)}
                color={Colors[theme].secondary}
              />
              <ThemedText size={14} style={styles.locationText}>
                {brand?.location?.address ||
                  brand?.location?.city ||
                  t("client.brandDetails.locationNotSpecified")}
              </ThemedText>
            </View>
          )}

          {brand?.phone && (
            <View style={styles.contactContainer}>
              <AntDesign
                name="phone"
                size={scale(16)}
                color={Colors[theme].secondary}
              />
              <ThemedText size={14} style={styles.contactText}>
                {brand?.phone}
              </ThemedText>
            </View>
          )}
        </View>

        <View style={styles.section}>
          <View style={styles.reviewsTitleContainer}>
            <ThemedText type="bold" size={22}>
              {t("client.brandDetails.reviews")} ({reviewsCount})
            </ThemedText>
            <TouchableOpacity
              onPress={() => setReviewSheetVisible(true)}
              style={styles.addReviewButton}
            >
              <AntDesign name="plus" size={20} color={Colors[theme].primary} />
            </TouchableOpacity>
          </View>
          {hasReviews ? brand?.reviews.map(renderReview) : <EmptyReviewList />}
        </View>
      </MainContainerForScreens>

      <BottomSheetAddReview
        isVisible={isReviewSheetVisible}
        onClose={() => {
          setReviewSheetVisible(false);
          refetch();
        }}
        id={id}
      />
    </View>
  );
};

const getStyles = (theme: AppTheme) =>
  StyleSheet.create({
    headerSection: { alignItems: "center" },
    brandLogo: {
      width: scale(100),
      height: scale(100),
      borderRadius: scale(50),
    },
    brandTitle: {
      marginTop: scale(10),
      textAlign: "center",
    },
    topRightHeartButton: {
      position: "absolute",
      top: scale(50),
      right: scale(20),
      zIndex: 1000,
      padding: scale(10),
      backgroundColor: Colors[theme].background,
      borderRadius: scale(25),
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    brandSubtitle: {
      marginTop: scale(4),
      color: Colors[theme].secondary,
      textAlign: "center",
    },
    brandRating: {
      marginTop: scale(4),
      color: Colors[theme].secondary,
      textAlign: "center",
    },
    viewProductsButton: {
      marginTop: scale(15),
      paddingVertical: scale(10),
      borderRadius: scale(20),
      width: "100%",
      alignItems: "center",
      justifyContent: "center",
    },
    section: {
      marginTop: scale(30),
    },
    reviewsTitleContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      marginBottom: scale(10),
    },
    addReviewButton: {
      padding: scale(8),
      borderRadius: scale(20),
      backgroundColor: Colors[theme].background,
      borderWidth: 1,
      borderColor: Colors[theme].primary,
    },
    vendorInfo: {
      flexDirection: "row",
      alignItems: "center",
      marginTop: scale(10),
    },
    reviewAvatarWrapper: {
      width: scale(30),
      height: scale(30),
      borderRadius: scale(15),
      backgroundColor: Colors[theme].primary,
      justifyContent: "center",
      alignItems: "center",
    },
    avatarText: {
      textAlign: "center",
    },
    vendorDetails: {
      flex: 1,
      marginLeft: scale(10),
    },
    secondaryText: {
      color: Colors[theme].secondary,
    },
    aboutText: {
      marginTop: scale(10),
      lineHeight: scale(20),
    },
    locationContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginTop: scale(8),
    },
    locationText: {
      marginLeft: scale(6),
    },
    contactContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginTop: scale(4),
    },
    contactText: {
      marginLeft: scale(6),
    },
    reviewContainer: {
      marginTop: scale(15),
      borderRadius: scale(10),
      padding: scale(15),
    },
    reviewHeader: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: scale(8),
    },
    reviewUserInfo: {
      marginLeft: scale(10),
      flex: 1,
    },
    starContainer: {
      flexDirection: "row",
      marginBottom: scale(8),
    },
    starIcon: {
      marginRight: scale(2),
    },
    reviewText: {
      marginBottom: scale(10),
      lineHeight: scale(20),
    },
    reviewActions: {
      flexDirection: "row",
    },
    actionButton: {
      flexDirection: "row",
      alignItems: "center",
      marginRight: scale(15),
    },
    actionText: {
      color: "#aaa",
      marginLeft: scale(4),
    },
  });

export default Brand;
