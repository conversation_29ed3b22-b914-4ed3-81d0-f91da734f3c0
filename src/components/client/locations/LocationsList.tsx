import { InfiniteScrollList } from "@/src/components/shared/InfiniteScrollList";
import { useLanguage } from "@/src/context/LanguageContext";
import useDeleteLocationMutation from "@/src/services/querys/client/useDeleteLocation";
import useGetLocations from "@/src/services/querys/client/useGetLocations";
import { ILocation } from "@/src/types/location";
import React, { useMemo, useState } from "react";
import { StyleSheet, View } from "react-native";
import EditLocationBottomSheet from "../../bottom-sheet/EditLocationBottomSheet";
import SkeletonLocationList from "../../loader/SkeletonLocationList";
import { DeleteConfirmationModal } from "../../model/DeleteConfirmationModal";
import EmptyLocationList from "../../ui/EmptyLocationList";
import { ErrorComponent } from "../../ui/ErrorComponent";
import { ThemedText } from "../../ui/ThemedText";
import LocationCard from "./LocationCard";

const LocationsList = () => {
  const [refreshing, setRefreshing] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [editBottomSheetVisible, setEditBottomSheetVisible] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<ILocation | null>(
    null
  );

  const deleteLocationMutation = useDeleteLocationMutation();

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    error,
    refetch,
  } = useGetLocations();
  const { t } = useLanguage();
  const locations = useMemo(() => {
    return data?.pages?.flatMap((page) => page.data.items) || [];
  }, [data]);
  const totalItems = data?.pages?.[0]?.data?.totalItems || 0;

  const handleRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const handleRefetch = async () => await refetch();

  const handleEdit = (location: ILocation) => {
    setSelectedLocation(location);
    setEditBottomSheetVisible(true);
  };

  const handleDelete = (location: ILocation) => {
    setSelectedLocation(location);
    setDeleteModalVisible(true);
  };

  const handleConfirmDelete = async () => {
    if (!selectedLocation?._id) return;

    try {
      await deleteLocationMutation.mutateAsync({ id: selectedLocation._id });
      setDeleteModalVisible(false);
      setSelectedLocation(null);
    } catch (error) {
      console.error("Failed to delete location:", error);
    }
  };

  const handleLocationUpdate = (updatedLocation: ILocation) => {
    // The mutation will handle cache invalidation
    setEditBottomSheetVisible(false);
    setSelectedLocation(null);
  };

  const handleCloseDeleteModal = () => {
    setDeleteModalVisible(false);
    setSelectedLocation(null);
  };

  const handleCloseEditBottomSheet = () => {
    setEditBottomSheetVisible(false);
    setSelectedLocation(null);
  };

  if (isError) {
    return (
      <ErrorComponent
        error={t("backend.server_error")}
        onRetry={handleRefetch}
      />
    );
  }

  return (
    <View style={styles.locationsSection}>
      <ThemedText type="bold" size={18} style={{ marginBottom: 16 }}>
        {t("locations.title")} ({totalItems})
      </ThemedText>
      <InfiniteScrollList
        data={locations}
        isLoading={isLoading}
        isFetchingNextPage={isFetchingNextPage}
        hasNextPage={hasNextPage}
        fetchNextPage={fetchNextPage}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        estimatedItemSize={100}
        renderItem={({ item }) => (
          <LocationCard
            item={item}
            onEdit={handleEdit}
            onDelete={handleDelete}
          />
        )}
        skeletonComponent={<SkeletonLocationList />}
        emptyComponent={<EmptyLocationList />}
        count={5}
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        visible={deleteModalVisible}
        onClose={handleCloseDeleteModal}
        onConfirm={handleConfirmDelete}
        title="Delete Location"
        message={`Are you sure you want to delete "${selectedLocation?.address}"? This action cannot be undone.`}
        isLoading={deleteLocationMutation.isPending}
        itemName={selectedLocation?.address}
        itemType="location"
      />

      {/* Edit Location Bottom Sheet */}
      <EditLocationBottomSheet
        isVisible={editBottomSheetVisible}
        onClose={handleCloseEditBottomSheet}
        location={selectedLocation}
        onLocationUpdate={handleLocationUpdate}
      />
    </View>
  );
};

export default LocationsList;

const styles = StyleSheet.create({
  locationsSection: {
    flex: 1,
  },
});
