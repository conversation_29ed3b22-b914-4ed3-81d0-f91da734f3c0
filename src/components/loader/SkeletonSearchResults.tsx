import { scale } from "@/src/_helper/Scaler";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import { StyleSheet, View } from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from "react-native-reanimated";

interface SkeletonSearchResultsProps {
  count?: number;
}

const SkeletonSearchResults: React.FC<SkeletonSearchResultsProps> = ({
  count = 1,
}) => {
  const { currentTheme } = useTheme();
  const pulse = useSharedValue(0.5);

  React.useEffect(() => {
    pulse.value = withRepeat(
      withTiming(1, { duration: 700 }),
      -1,
      true // reverse animation
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: pulse.value,
  }));

  const background = Colors[currentTheme ?? "dark"].border;

  return (
    <>
      {Array(count)
        .fill(null)
        .map((_, idx) => (
          <ThemedView
            key={idx}
            style={[
              styles.container,
              { backgroundColor: Colors[currentTheme ?? "dark"].thirdary },
            ]}
          >
            {/* Image Placeholder */}
            <Animated.View
              style={[
                styles.imagePlaceholder,
                { backgroundColor: background },
                animatedStyle,
              ]}
            />

            {/* Content Placeholder */}
            <View style={styles.contentContainer}>
              <View style={styles.textContainer}>
                <Animated.View
                  style={[
                    styles.titleLine,
                    { backgroundColor: background },
                    animatedStyle,
                  ]}
                />
                <Animated.View
                  style={[
                    styles.subtitleLine,
                    { backgroundColor: background },
                    animatedStyle,
                  ]}
                />
                <Animated.View
                  style={[
                    styles.priceLine,
                    { backgroundColor: background },
                    animatedStyle,
                  ]}
                />
              </View>
            </View>
          </ThemedView>
        ))}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    padding: scale(16),
    marginBottom: scale(12),
    borderRadius: scale(16),
    overflow: "hidden",
  },
  imagePlaceholder: {
    width: scale(56),
    height: scale(56),
    borderRadius: scale(12),
    marginRight: scale(16),
  },
  contentContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  textContainer: {
    flex: 1,
  },
  titleLine: {
    height: scale(14),
    borderRadius: scale(7),
    marginBottom: scale(8),
    width: "70%",
  },
  subtitleLine: {
    height: scale(12),
    borderRadius: scale(6),
    marginBottom: scale(6),
    width: "50%",
  },
  priceLine: {
    height: scale(12),
    borderRadius: scale(6),
    width: "30%",
  },
});

export default SkeletonSearchResults;
