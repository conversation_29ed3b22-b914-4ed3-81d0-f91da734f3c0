import { scale } from "@/src/_helper/Scaler";
import { addProductSchema } from "@/src/_helper/validator/VendorValidator";
import BrandBottomSheet from "@/src/components/bottom-sheet/BrandsBottomSheet";
import CategoryBottomSheet from "@/src/components/bottom-sheet/CategorySelectorBottomSheet";
import GalleryPermission from "@/src/components/bottom-sheet/GalleryPermission";
import DefaultButton from "@/src/components/buttons/Default";
import { MainContainerForScreensWithInput } from "@/src/components/containers/MainContainerForScreensWithInput";
import BrandInput from "@/src/components/inputs/BrandSelector";
import MultiImageUploader from "@/src/components/inputs/MultiImageUpload";
import CategoryInput from "@/src/components/inputs/SelectorInput";
import TextInput from "@/src/components/inputs/TextInput";
import Loader from "@/src/components/loader/Loader";
import { ErrorComponent } from "@/src/components/ui/ErrorComponent";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { useImagePicker } from "@/src/hooks/useImagePicker";
import useGetProductDetails from "@/src/services/querys/vendor/useGetProductDetails";
import useUpdateProductMutation from "@/src/services/querys/vendor/useUpdateProductMutation";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Formik } from "formik";
import React, { useEffect, useRef, useState } from "react";
import { StyleSheet, View } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";

import { EditProductFormValues } from "@/src/types";

export default function EditProduct() {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const theme = currentTheme ?? "dark";
  const router = useRouter();
  const { id } = useLocalSearchParams() as { id: string };

  // Fetch product details
  const { data, isLoading, isError, error, refetch } = useGetProductDetails(id);
  const updateMutation = useUpdateProductMutation();

  const {
    imageUris,
    pickImages,
    removeImage,
    showPermissionUI,
    closeUI,
    setImageUris,
  } = useImagePicker();

  const [showCategorySheet, setShowCategorySheet] = useState(false);
  const [showBrandSheet, setShowBrandSheet] = useState(false);
  const [selectedBrandName, setSelectedBrandName] = useState("");
  const [isFormInitialized, setIsFormInitialized] = useState(false);
  const [selectedCategoryName, setSelectedCategoryName] = useState("");
  const handleRefetch = async () => await refetch(); // Trigger a re-fetch of the product details
  const formikSetFieldValueRef = useRef<
    null | ((field: string, value: any) => void)
  >(null);

  // Initialize form data when product data is loaded
  useEffect(() => {
    if (data?.data?.product && !isFormInitialized) {
      const product = data?.data?.product;

      // Handle brand name - support both object and string formats
      const brandName =
        typeof product?.brand === "object" && product?.brand?.name
          ? product?.brand?.name
          : typeof product?.brand === "string"
          ? product?.brand
          : "";
      setSelectedBrandName(brandName);

      // Handle category name - support both object and string formats
      const categoryName =
        typeof product?.category === "object" && product?.category?.name
          ? product?.category?.name
          : typeof product?.category === "string"
          ? product?.category
          : "";
      setSelectedCategoryName(categoryName);

      if (product?.images && product?.images.length > 0) {
        setImageUris(product?.images);
      }

      setIsFormInitialized(true);
    }
  }, [data, isFormInitialized, setImageUris]);

  // Sync imageUris with Formik when images change
  useEffect(() => {
    if (formikSetFieldValueRef.current && isFormInitialized) {
      formikSetFieldValueRef.current("images", imageUris);
    }
  }, [imageUris, isFormInitialized]);

  const handleSubmit = async (values: EditProductFormValues) => {
    try {
      values.id = id;
      await updateMutation.mutateAsync({
        ...values,
      });
    } catch (error) {
      console.error("Update error:", error);
    }
  };

  const handleCategorySelect = (categoryId: string, categoryName: string) => {
    formikSetFieldValueRef.current?.("category", categoryId);
    setSelectedCategoryName(categoryName);
  };

  const handleBrandSelect = (brandId: string, brandName: string) => {
    formikSetFieldValueRef.current?.("brand", brandId);
    setSelectedBrandName(brandName);
  };

  const handlePickImages = async () => {
    try {
      await pickImages();
    } catch (error) {
      console.error("Error picking images:", error);
    }
  };

  const handleRemoveImage = (uri: string) => {
    removeImage(uri);
  };

  const handleNumericInput = (
    field: string,
    text: string,
    setFieldValue: any
  ) => {
    if (text === "" || /^\d*\.?\d*$/.test(text)) {
      setFieldValue(field, text);
    }
  };

  // Loading state
  if (isLoading) {
    return <Loader />;
  }

  // Error state
  if (isError) {
    return (
      <ErrorComponent
        error={t("backend.server_error")}
        onRetry={handleRefetch}
      />
    );
  }

  // No data state
  if (!data?.data?.product) {
    return (
      <ErrorComponent
        error={t("backend.server_error")}
        onRetry={handleRefetch}
      />
    );
  }

  const product = data.data.product;

  // Helper function to safely get ID from object or string
  const getCategoryId = () => {
    if (typeof product?.category === "object" && product?.category?._id) {
      return product?.category?._id;
    }
    if (typeof product?.category === "string") {
      return product?.category;
    }
    return "";
  };

  const getBrandId = () => {
    if (typeof product?.brand === "object" && product?.brand?._id) {
      return product?.brand?._id;
    }
    if (typeof product?.brand === "string") {
      return product?.brand;
    }
    return "";
  };

  // Initial values from product data
  const initialValues: EditProductFormValues = {
    id: "",
    name: product?.name || "",
    description: product?.description || "",
    category: getCategoryId(),
    brand: getBrandId(),
    price: product?.price?.toString() || "",
    images: product?.images || [],
  };

  return (
    <GestureHandlerRootView>
      <MainContainerForScreensWithInput>
        <Formik<EditProductFormValues>
          initialValues={initialValues}
          validationSchema={addProductSchema}
          onSubmit={handleSubmit}
          enableReinitialize={true} // Important: allows form to update when initialValues change
        >
          {(formikProps) => {
            const {
              values,
              errors,
              touched,
              handleChange,
              handleSubmit,
              setFieldValue,
            } = formikProps;

            // Set the ref for setFieldValue
            if (!formikSetFieldValueRef.current) {
              formikSetFieldValueRef.current = setFieldValue;
            }

            return (
              <View style={styles.form}>
                <TextInput
                  value={values.name}
                  onChangeText={handleChange("name")}
                  placeholder={t("vendor.productForm.placeholders.productName")}
                  error={touched.name && errors.name}
                />

                <TextInput
                  value={values.description}
                  onChangeText={handleChange("description")}
                  placeholder={t(
                    "vendor.productForm.placeholders.productDescription"
                  )}
                  multiline
                  error={touched.description && errors.description}
                />

                <CategoryInput
                  selectedCategory={selectedCategoryName}
                  onPress={() => setShowCategorySheet(true)}
                  error={touched.category && errors.category}
                  placeholder={t(
                    "vendor.productForm.placeholders.selectCategory"
                  )}
                />

                <BrandInput
                  selectedBrandName={selectedBrandName}
                  onPress={() => setShowBrandSheet(true)}
                  error={touched.brand && errors.brand}
                />

                <TextInput
                  value={values.price}
                  onChangeText={(text) =>
                    handleNumericInput("price", text, setFieldValue)
                  }
                  placeholder="Price *"
                  keyboardType="numeric"
                  error={touched.price && errors.price}
                />

                <MultiImageUploader
                  images={imageUris}
                  onPickImages={handlePickImages}
                  onRemoveImage={handleRemoveImage}
                  error={
                    touched.images && typeof errors.images === "string"
                      ? errors.images
                      : undefined
                  }
                />

                <View style={styles.buttonContainer}>
                  <DefaultButton
                    title={t("buttons.cancel")}
                    onPress={() => router.back()}
                    active
                    style={[
                      styles.button,
                      { backgroundColor: Colors[theme].secondary + "40" },
                    ]}
                    disabled={updateMutation.isPending}
                  />

                  <DefaultButton
                    title={t("buttons.updateProduct")}
                    onPress={handleSubmit}
                    style={styles.button}
                    isSubmitting={updateMutation.isPending}
                    disabled={updateMutation.isPending}
                  />
                </View>
              </View>
            );
          }}
        </Formik>
      </MainContainerForScreensWithInput>

      {/* Bottom Sheets */}
      <GalleryPermission isVisible={showPermissionUI} onClose={closeUI} />
      <CategoryBottomSheet
        isVisible={showCategorySheet}
        onClose={() => setShowCategorySheet(false)}
        onCategorySelect={handleCategorySelect}
      />
      <BrandBottomSheet
        isVisible={showBrandSheet}
        onClose={() => setShowBrandSheet(false)}
        onBrandSelect={handleBrandSelect}
      />
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  form: {
    flex: 1,
    gap: scale(12),
  },
  centered: {
    justifyContent: "center",
    alignItems: "center",
  },
  buttonContainer: {
    flexDirection: "row",
    gap: scale(12),
    marginTop: scale(20),
  },
  button: {
    flex: 1,
  },
});
