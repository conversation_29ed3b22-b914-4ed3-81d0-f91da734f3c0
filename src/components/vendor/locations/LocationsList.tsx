import { InfiniteScrollList } from "@/src/components/shared/InfiniteScrollList";
import { useLanguage } from "@/src/context/LanguageContext";
import useGetLocations from "@/src/services/querys/client/useGetLocations";
import React, { useMemo, useState } from "react";
import { StyleSheet, View } from "react-native";
import SkeletonLocationList from "../../loader/SkeletonLocationList";
import EmptyLocationList from "../../ui/EmptyLocationList";
import { ErrorComponent } from "../../ui/ErrorComponent";
import LocationCard from "./LocationCard";

const LocationsList = () => {
  const [refreshing, setRefreshing] = useState(false);

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    error,
    refetch,
  } = useGetLocations();
  const { t } = useLanguage();
  const locations = useMemo(() => {
    return data?.pages?.flatMap((page) => page.data.items) || [];
  }, [data]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const handleRefetch = async () => await refetch();

  if (isError) {
    return (
      <ErrorComponent
        error={t("backend.server_error")}
        onRetry={handleRefetch}
      />
    );
  }

  return (
    <View style={styles.locationsSection}>
      <InfiniteScrollList
        data={locations}
        isLoading={isLoading}
        isFetchingNextPage={isFetchingNextPage}
        hasNextPage={hasNextPage}
        fetchNextPage={fetchNextPage}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        estimatedItemSize={100}
        renderItem={({ item }) => <LocationCard item={item} />}
        skeletonComponent={<SkeletonLocationList />}
        emptyComponent={<EmptyLocationList />}
        count={5}
      />
    </View>
  );
};

export default LocationsList;

const styles = StyleSheet.create({
  locationsSection: {
    flex: 1,
  },
});
