import { scale, scaleFont } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import useGetCategories from "@/src/services/querys/vendor/useGetCategories";
import { ICategory } from "@/src/types";
import { Ionicons } from "@expo/vector-icons";
import React, { useEffect, useMemo } from "react";
import {
  Dimensions,
  Keyboard,
  Pressable,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from "react-native-reanimated";
import SkeletonCategoryList from "../loader/SkeletonCategoryList";
import { InfiniteScrollList } from "../shared/InfiniteScrollList";
import EmptyCategories from "../ui/EmptyCategories";
import { ErrorComponent } from "../ui/ErrorComponent";

const { height: SCREEN_HEIGHT } = Dimensions.get("window");

interface CategoryBottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
  onCategorySelect: (categoryId: string, categoryName: string) => void;
}

export default function CategoryBottomSheet({
  isVisible,
  onClose,
  onCategorySelect,
}: CategoryBottomSheetProps) {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();

  const translateY = useSharedValue(SCREEN_HEIGHT);
  const context = useSharedValue({ y: 0 });

  const {
    data,
    isLoading,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
    refetch,
    isRefetching,
    isError,
    error,
  } = useGetCategories();

  const categories = useMemo(() => {
    if (!data?.pages) return [];
    return data.pages.flatMap((page) => page?.data?.items || []);
  }, [data]);

  const gesture = Gesture.Pan()
    .onStart(() => {
      context.value = { y: translateY.value };
    })
    .onUpdate((e) => {
      if (e.translationY > 0) {
        translateY.value = Math.min(
          e.translationY + context.value.y,
          SCREEN_HEIGHT
        );
      }
    })
    .onEnd((e) => {
      if (e.translationY > SCREEN_HEIGHT * 0.3) {
        translateY.value = withSpring(SCREEN_HEIGHT);
        runOnJS(onClose)();
      } else {
        translateY.value = withSpring(0);
      }
    });

  const reanimatedBottomStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
  }));

  const overlayStyle = useAnimatedStyle(() => ({
    opacity: isVisible && translateY.value < SCREEN_HEIGHT * 0.5 ? 0.4 : 0,
  }));

  useEffect(() => {
    Keyboard.dismiss();
    translateY.value = withSpring(isVisible ? 0 : SCREEN_HEIGHT);
  }, [isVisible]);

  const handleClose = () => {
    onClose();
  };

  const handleCategorySelect = (categoryId: string, categoryName: string) => {
    // Add null checks before calling the callback
    if (categoryId && categoryName) {
      onCategorySelect(categoryId, categoryName);
      handleClose();
    }
  };

  const renderCategoryItem = ({ item }: { item: ICategory }) => {
    // Add safety checks for item properties
    if (!item || !item._id || !item.name) {
      return null;
    }

    return (
      <TouchableOpacity
        style={[
          styles.categoryItem,
          {
            borderBottomColor:
              Colors[currentTheme ?? "dark"]?.border || "#E9E9E9",
          },
        ]}
        onPress={() => handleCategorySelect(item._id, item.name)}
      >
        <View>
          <ThemedText type="bold" size={16}>
            {item.name}
          </ThemedText>
          {item.subcategories && item.subcategories.length > 0 && (
            <ThemedText size={12} style={styles.subcategoriesText}>
              {item.subcategories.slice(0, 3).join(", ")}
              {item.subcategories.length > 3 && "..."}
            </ThemedText>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  if (!isVisible) return null;
  const handleRefetch = async () => await refetch(); // Wait for the refetch operation to complete
  return (
    <>
      {/* Overlay */}
      <Animated.View
        style={[
          styles.overlay,
          overlayStyle,
          { backgroundColor: Colors[currentTheme ?? "dark"].overlay },
        ]}
        pointerEvents="auto"
      >
        <Pressable
          style={StyleSheet.absoluteFillObject}
          onPress={handleClose}
        />
      </Animated.View>

      {/* Bottom Sheet */}
      <Animated.View
        style={[
          styles.bottomsheet_container,
          reanimatedBottomStyle,
          {
            backgroundColor:
              Colors[currentTheme ?? "dark"]?.background || "#FFFFFF",
          },
        ]}
      >
        {/* Header with Drag Handle */}
        <GestureDetector gesture={gesture}>
          <View style={styles.dragArea}>
            <View
              style={[
                styles.line,
                { backgroundColor: Colors[currentTheme ?? "dark"].separator },
              ]}
            />
            <ThemedText type="bold" size={18} style={styles.headerTitle}>
              {t("bottomSheets.categories.title")}
            </ThemedText>
          </View>
        </GestureDetector>

        {/* Category Content */}
        {isError ? (
          <ErrorComponent
            error={t("backend.server_error")}
            onRetry={handleRefetch}
          />
        ) : (
          <View style={styles.categoryListContainer}>
            <InfiniteScrollList
              data={categories}
              renderItem={renderCategoryItem}
              keyExtractor={(item) => item?._id || Math.random().toString()}
              estimatedItemSize={80}
              isLoading={isLoading}
              isFetchingNextPage={isFetchingNextPage}
              hasNextPage={hasNextPage}
              fetchNextPage={fetchNextPage}
              refreshing={isRefetching}
              onRefresh={refetch}
              skeletonComponent={<SkeletonCategoryList />}
              emptyComponent={<EmptyCategories />}
              contentContainerStyle={{ paddingBottom: scale(20) }}
              showsVerticalScrollIndicator={false}
              count={5}
            />
          </View>
        )}

        {/* Manual Close Button */}
        <Pressable onPress={handleClose} style={styles.closeButton}>
          <Ionicons name="close" size={scaleFont(24)} />
        </Pressable>
      </Animated.View>
    </>
  );
}

const styles = StyleSheet.create({
  bottomsheet_container: {
    width: "100%",
    height: SCREEN_HEIGHT * 0.7,
    position: "absolute",
    bottom: 0,
    zIndex: 999,
    borderTopEndRadius: scale(25),
    borderTopStartRadius: scale(25),
    paddingHorizontal: scale(5),
  },
  overlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 998,
  },
  dragArea: {
    paddingHorizontal: scale(15),
    alignItems: "center",
  },
  line: {
    width: scale(75),
    height: scale(4),
    borderRadius: 20,
    alignSelf: "center",
    marginVertical: scale(10),
  },
  headerTitle: {
    marginBottom: scale(15),
  },
  categoryListContainer: {
    flex: 1,
    paddingHorizontal: scale(20),
  },
  categoryItem: {
    paddingVertical: scale(15),
    paddingHorizontal: scale(10),
    borderBottomWidth: 1,
  },
  subcategoriesText: {
    marginTop: 4,
    opacity: 0.7,
  },
  closeButton: {
    position: "absolute",
    top: scale(-50),
    right: scale(10),
    height: scale(40),
    width: scale(40),
    backgroundColor: "white",
    borderRadius: scale(20),
    justifyContent: "center",
    alignItems: "center",
  },
});
